import React, { useState } from "react";
import { CheckResult } from "@/lib/checkDefinitions";
import { CheckCircle, XCircle, ChevronDown, ChevronUp } from "lucide-react";
import { Separator } from "@/components/ui/separator";

interface AuditReportItemProps {
  title: string;
  result: CheckResult;
}

// Helper function to format JSON data as readable text
const formatJsonAsText = (data: any): string => {
  if (!data) return '';

  try {
    // If it's already a string, try to parse it as JSON
    if (typeof data === 'string') {
      try {
        data = JSON.parse(data);
      } catch {
        // If parsing fails, return the string as is
        return data;
      }
    }

    // If it's an object, format it nicely
    if (typeof data === 'object') {
      let formatted = '';

      for (const [key, value] of Object.entries(data)) {
        // Convert camelCase and snake_case to readable format
        const readableKey = key
          .replace(/([A-Z])/g, ' $1')
          .replace(/_/g, ' ')
          .replace(/^./, str => str.toUpperCase())
          .trim();

        if (typeof value === 'object' && value !== null) {
          formatted += `${readableKey}:\n`;
          for (const [subKey, subValue] of Object.entries(value)) {
            const readableSubKey = subKey
              .replace(/([A-Z])/g, ' $1')
              .replace(/_/g, ' ')
              .replace(/^./, str => str.toUpperCase())
              .trim();

            if (typeof subValue === 'boolean') {
              formatted += `  ${readableSubKey}: ${subValue ? 'Yes' : 'No'}\n`;
            } else if (subValue === null || subValue === undefined) {
              formatted += `  ${readableSubKey}: N/A\n`;
            } else {
              formatted += `  ${readableSubKey}: ${subValue}\n`;
            }
          }
          formatted += '\n';
        } else {
          if (typeof value === 'boolean') {
            formatted += `${readableKey}: ${value ? 'Yes' : 'No'}\n`;
          } else if (value === null || value === undefined) {
            formatted += `${readableKey}: N/A\n`;
          } else {
            // Handle special formatting for currency values
            if (typeof value === 'string' && value.includes('₹')) {
              formatted += `${readableKey}: ${value}\n`;
            } else if (typeof value === 'number' && key.toLowerCase().includes('amount')) {
              formatted += `${readableKey}: ₹${value} crores\n`;
            } else {
              formatted += `${readableKey}: ${value}\n`;
            }
          }
        }
      }

      return formatted.trim();
    }

    return String(data);
  } catch (error) {
    console.warn('Error formatting JSON data:', error);
    return String(data);
  }
};

const AuditReportItem: React.FC<AuditReportItemProps> = ({ title, result }) => {
  const [showDetails, setShowDetails] = useState(false);

  if (!result) return null;

  return (
    <div className="mb-4">
      <div className="flex items-start">
        {result.isCompliant ? (
          <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
        ) : (
          <XCircle className="h-5 w-5 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
        )}
        <div className="flex-1">
          <h4 className="font-medium text-base">{title}</h4>

          <div className="mb-2">
            {result.isCompliant ? (
              <div className="text-green-600 font-medium flex items-center">
                <span className="inline-block w-4 h-4 mr-1 bg-green-100 rounded-sm border border-green-300 flex items-center justify-center">
                  <span className="text-green-600 text-xs">✓</span>
                </span>
                <span>Compliant</span>
              </div>
            ) : (
              <div className="text-red-600 font-medium flex items-center">
                <span className="inline-block w-4 h-4 mr-1 bg-red-100 rounded-sm border border-red-300 flex items-center justify-center">
                  <span className="text-red-600 text-xs">✗</span>
                </span>
                <span>Non-Compliant</span>
              </div>
            )}
          </div>

          <div className="text-sm">
            <strong>Explanation:</strong>
            <span className="text-gray-700 whitespace-pre-line ml-1">{formatJsonAsText(result.explanation)}</span>
          </div>

          {/* Special handling for profit/loss check */}
          {title === "Profit Or Loss In Opinion" && result.extractedData && (
            <div className="mt-2 text-sm">
              <div className="flex flex-col space-y-1">
                <div className="flex items-center">
                  <strong className="mr-1">Financial Year:</strong>
                  <span className="text-gray-700">{result.extractedData.financialYear}</span>
                </div>
                <div className="flex items-center">
                  <strong className="mr-1">Expected:</strong>
                  <span className="text-gray-700 capitalize">{result.extractedData.expectedValue}</span>
                </div>
                <div className="flex items-center">
                  <strong className="mr-1">Found:</strong>
                  <span className={`capitalize ${result.extractedData.expectedValue === result.extractedData.foundValue ? 'text-green-600' : 'text-red-600'}`}>
                    {result.extractedData.foundValue}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Special handling for Section 197(16) Reference check */}
          {title === "Section 197(16) Reference" && !result.isCompliant && (
            <div className="mt-2 text-sm">
              <div className="flex flex-col space-y-1">
                <div className="flex items-center">
                  <span className="text-red-600">
                    The audit report does not contain the required Section 197(16) reference for director remuneration disclosures.
                  </span>
                </div>
              </div>
            </div>
          )}

          {(result.detail || result.evidence || result.extractedData?.quote) && (
            <>
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="text-blue-600 text-sm mt-2 hover:underline flex items-center"
              >
                {showDetails ? "Hide details" : "Show details"}
                {showDetails ? (
                  <ChevronUp className="h-3 w-3 ml-1" />
                ) : (
                  <ChevronDown className="h-3 w-3 ml-1" />
                )}
              </button>

              {showDetails && (
                <div className="mt-2 bg-gray-50 p-3 rounded-md text-sm border border-gray-200">
                  {title === "Profit Or Loss In Opinion" && result.extractedData?.quote && (
                    <div className="mb-2">
                      <p className="font-medium mb-1">Quote from Opinion Paragraph:</p>
                      <p className="text-gray-700 whitespace-pre-line italic">"{result.extractedData.quote}"</p>
                    </div>
                  )}
                  {title === "Section 197(16) Reference" && result.isCompliant && result.extractedData?.quote && (
                    <div className="mb-2">
                      <p className="font-medium mb-1">Section 197(16) Reference:</p>
                      <p className="text-gray-700 whitespace-pre-line italic">"{result.extractedData.quote}"</p>
                    </div>
                  )}
                  {result.detail && (
                    <div className="mb-2">
                      <p className="font-medium mb-1">Details:</p>
                      <p className="text-gray-700 whitespace-pre-line">{formatJsonAsText(result.detail)}</p>
                    </div>
                  )}
                  {result.evidence && (
                    <div className="mb-2">
                      <p className="font-medium mb-1">Evidence:</p>
                      <p className="text-gray-700 whitespace-pre-line">{formatJsonAsText(result.evidence)}</p>
                    </div>
                  )}
                  {result.extractedData && typeof result.extractedData === 'object' && !result.extractedData.quote && (
                    <div className="mb-2">
                      <p className="font-medium mb-1">Extracted Data:</p>
                      <p className="text-gray-700 whitespace-pre-line">{formatJsonAsText(result.extractedData)}</p>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>
      <Separator className="mt-3" />
    </div>
  );
};

export default AuditReportItem;

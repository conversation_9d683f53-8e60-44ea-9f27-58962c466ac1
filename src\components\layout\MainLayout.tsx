// Update your MainLayout.tsx file with this version:

import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import DashboardSidebar from "./DashboardSidebar";
import Header from "./Header";
import LoadingScreen from "@/components/common/LoadingScreen";

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  
  // Check if this is the landing page or a content page that needs full-width layout
  const isLandingPage = location.pathname === '/';
  const isContentPage = ['/blog', '/resources', '/case-studies', '/terms', '/privacy', '/cookies', '/security', '/compliance', '/gdpr'].includes(location.pathname);
    // Debug info
  useEffect(() => {
    console.log("MainLayout rendered with currentUser:", currentUser ? "Logged in" : "Not logged in");
    console.log("Current location:", location.pathname);
    console.log("Is landing page:", isLandingPage);
    console.log("Is content page:", isContentPage);
  }, [currentUser, location, isLandingPage, isContentPage]);
  
  // Handle loading state
  useEffect(() => {
    // Show loading state when changing routes
    setLoading(true);
    
    // Hide loading after a short delay to simulate content loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);
    
    return () => clearTimeout(timer);
  }, [location.pathname]);

  // If user is logged in and at the root path, redirect them to the dashboard
  useEffect(() => {
    if (currentUser && location.pathname === '/') {
      console.log("User is logged in and at root path, redirecting to dashboard");
      navigate('/dashboard');
    }
  }, [currentUser, location, navigate]);

  // If this is the landing page, render without containers
  if (isLandingPage) {
    return (
      <div className="min-h-screen w-full">
        {children}
      </div>
    );
  }
  
  // If this is a content page, render with minimal container
  if (isContentPage) {
    return (
      <div className="min-h-screen w-full bg-slate-900">
        {children}
      </div>
    );
  }

  // Normal layout for other pages
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="flex min-h-[calc(100vh-64px)]">
        {/* Sidebar placeholder for spacing - only show when user is logged in */}
        {currentUser && <div id="sidebar-placeholder" className="w-64 flex-shrink-0 transition-all duration-300" />}

        <main className="flex-1 p-4 md:p-6 overflow-auto">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>

        {/* Actual Sidebar - Fixed Position - only show when user is logged in */}
        {currentUser && <DashboardSidebar />}
      </div>
    </div>
  );
};

export default MainLayout;
import React, { useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";

interface AuthLayoutProps {
  children: React.ReactNode;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  
  // Debug information
  useEffect(() => {
    console.log("AuthLayout rendered with currentUser:", currentUser ? "Logged in" : "Not logged in");
  }, [currentUser]);
  
  // Redirect to dashboard if user is already logged in
  useEffect(() => {
    if (currentUser) {
      console.log("User is already logged in, redirecting to dashboard");
      navigate('/dashboard');
    }
  }, [currentUser, navigate]);

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to="/" className="flex items-center gap-2">
              <div className="h-9 w-9 rounded-lg bg-primary flex items-center justify-center text-white font-bold text-lg">
                A
              </div>
              <span className="font-bold text-xl text-gray-900 hidden sm:block">
                Audit Report Guardian AI
              </span>
            </Link>
          </div>
        </div>
      </header>

      <main className="flex-1 flex items-center justify-center p-4 md:p-6 overflow-auto relative">
        {/* Background decorative elements */}
        <div className="absolute top-20 right-10 w-32 h-32 bg-blue-400/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-10 w-48 h-48 bg-blue-300/20 rounded-full blur-3xl"></div>

        {/* Content */}
        <div className="relative z-10 w-full max-w-7xl mx-auto">
          {children}
        </div>
      </main>
    </div>
  );
};

export default AuthLayout;

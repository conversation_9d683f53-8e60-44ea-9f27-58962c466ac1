import React from 'react';

// Example of how the JSON formatting will work
const FormatExample = () => {
  // Example JSON data like what you showed
  const exampleJsonData = {
    "auditReportReference": "1",
    "annexureABackReference": "1", 
    "referencesMatch": true,
    "auditReportText": "As required by the Companies (Auditors' Report) Order, 2020 (\"the Order\"), issued by the Central Government of India in terms of Section 143 (11) of the Act, we give in the \"Annexure A\" a statement on the matters specified in paragraphs 3 and 4 of the Order, to the extent applicable.",
    "annexureAText": "Referred to in paragraph 1 on 'Report on Other Legal and Regulatory Requirements' of our report of even date to the members of TVS Srichakra Limited (\"the Company\") on the standalone financial statements as of and for the year ended 31 March 2024."
  };

  const exampleJsonData2 = {
    "inventoriesNote": "Not Found",
    "goodsInTransitPresent": "Not Present", 
    "caroGoodsInTransitDisclosure": "Clause (ii)(a) Proper Disclosure"
  };

  const exampleJsonData3 = {
    "securedBorrowingsAmount": "₹0 crores",
    "amountMoreThan5Crores": "No (≤ 5 crores)",
    "caroQuarterlyReturnsDisclosure": "Clause (ii)(b) Present"
  };

  // Helper function to format JSON data as readable text (same as in AuditReportItem)
  const formatJsonAsText = (data: any): string => {
    if (!data) return '';
    
    try {
      // If it's already a string, try to parse it as JSON
      if (typeof data === 'string') {
        try {
          data = JSON.parse(data);
        } catch {
          // If parsing fails, return the string as is
          return data;
        }
      }
      
      // If it's an object, format it nicely
      if (typeof data === 'object') {
        let formatted = '';
        
        for (const [key, value] of Object.entries(data)) {
          // Convert camelCase and snake_case to readable format
          const readableKey = key
            .replace(/([A-Z])/g, ' $1')
            .replace(/_/g, ' ')
            .replace(/^./, str => str.toUpperCase())
            .trim();
          
          if (typeof value === 'object' && value !== null) {
            formatted += `${readableKey}:\n`;
            for (const [subKey, subValue] of Object.entries(value)) {
              const readableSubKey = subKey
                .replace(/([A-Z])/g, ' $1')
                .replace(/_/g, ' ')
                .replace(/^./, str => str.toUpperCase())
                .trim();
              
              if (typeof subValue === 'boolean') {
                formatted += `  ${readableSubKey}: ${subValue ? 'Yes' : 'No'}\n`;
              } else if (subValue === null || subValue === undefined) {
                formatted += `  ${readableSubKey}: N/A\n`;
              } else {
                formatted += `  ${readableSubKey}: ${subValue}\n`;
              }
            }
            formatted += '\n';
          } else {
            if (typeof value === 'boolean') {
              formatted += `${readableKey}: ${value ? 'Yes' : 'No'}\n`;
            } else if (value === null || value === undefined) {
              formatted += `${readableKey}: N/A\n`;
            } else {
              // Handle special formatting for currency values
              if (typeof value === 'string' && value.includes('₹')) {
                formatted += `${readableKey}: ${value}\n`;
              } else if (typeof value === 'number' && key.toLowerCase().includes('amount')) {
                formatted += `${readableKey}: ₹${value} crores\n`;
              } else {
                formatted += `${readableKey}: ${value}\n`;
              }
            }
          }
        }
        
        return formatted.trim();
      }
      
      return String(data);
    } catch (error) {
      console.warn('Error formatting JSON data:', error);
      return String(data);
    }
  };

  return (
    <div className="p-6 space-y-8">
      <h2 className="text-2xl font-bold">JSON to Text Formatting Examples</h2>
      
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-2">Example 1: Audit Report Reference Check</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">Before (JSON):</h4>
              <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto">
                {JSON.stringify(exampleJsonData, null, 2)}
              </pre>
            </div>
            <div>
              <h4 className="font-medium mb-2">After (Formatted Text):</h4>
              <div className="bg-blue-50 p-3 rounded text-sm whitespace-pre-line">
                {formatJsonAsText(exampleJsonData)}
              </div>
            </div>
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">Example 2: Inventory Check</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">Before (JSON):</h4>
              <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto">
                {JSON.stringify(exampleJsonData2, null, 2)}
              </pre>
            </div>
            <div>
              <h4 className="font-medium mb-2">After (Formatted Text):</h4>
              <div className="bg-blue-50 p-3 rounded text-sm whitespace-pre-line">
                {formatJsonAsText(exampleJsonData2)}
              </div>
            </div>
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">Example 3: Secured Borrowings Check</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">Before (JSON):</h4>
              <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto">
                {JSON.stringify(exampleJsonData3, null, 2)}
              </pre>
            </div>
            <div>
              <h4 className="font-medium mb-2">After (Formatted Text):</h4>
              <div className="bg-blue-50 p-3 rounded text-sm whitespace-pre-line">
                {formatJsonAsText(exampleJsonData3)}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FormatExample;

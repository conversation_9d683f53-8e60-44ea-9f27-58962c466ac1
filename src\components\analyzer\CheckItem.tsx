import React from 'react';
import { CheckResult } from '@/lib/googleAI2'; // Assuming CheckResult is defined here
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

// Define DocumentDetail if it's not globally available or imported
// This should match the interface in StreamlitStyleChecklist.tsx
interface DocumentDetail {
  name: string;
  id: string;
  url: string;
  path: string;
}

interface CheckItemProps {
  id: string;
  result: CheckResult;
  title: string; // Title for the check item
  associatedDocs?: DocumentDetail[]; // Optional: Documents associated with this check
}

const CheckItem: React.FC<CheckItemProps> = ({ id, result, title, associatedDocs }) => {
  const { isCompliant, explanation, details, severity } = result;

  // Determine icon and color based on compliance and severity
  let icon, colorClass, statusText;
  if (isCompliant) {
    icon = <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />;
    colorClass = 'text-green-700 bg-green-50 border-green-300';
    statusText = 'Compliant';
  } else {
    if (severity === 'Error' || severity === 'Critical') {
      icon = <XCircle className="h-5 w-5 text-red-500 flex-shrink-0" />;
      colorClass = 'text-red-700 bg-red-50 border-red-300';
      statusText = 'Non-Compliant';
    } else if (severity === 'Warning') {
      icon = <AlertCircle className="h-5 w-5 text-yellow-500 flex-shrink-0" />;
      colorClass = 'text-yellow-700 bg-yellow-50 border-yellow-300';
      statusText = 'Warning';
    } else {
      icon = <XCircle className="h-5 w-5 text-red-500 flex-shrink-0" />;
      colorClass = 'text-red-700 bg-red-50 border-red-300'; // Default to non-compliant red
      statusText = 'Non-Compliant';
    }
  }

  return (
    <div className={`mb-4 p-3 border rounded-md ${colorClass} shadow-sm`}>
      <div className="flex items-start">
        <div className="mt-0.5 mr-2">{icon}</div>
        <div className="flex-1">
          <h4 className="font-medium text-base text-gray-800">{title}</h4>
          
          <div className={`text-sm font-semibold mb-1 ${isCompliant ? 'text-green-600' : (severity === 'Warning' ? 'text-yellow-600' : 'text-red-600')}`}>
            {statusText}
          </div>

          {explanation && (
            <p className="text-sm text-gray-700 whitespace-pre-wrap">
              {explanation}
            </p>
          )}

          {details && (
            <div className="mt-2 text-xs text-gray-600 bg-gray-50 p-2 rounded">
              <p className="font-semibold mb-1">Details:</p>
              <pre className="whitespace-pre-wrap">{typeof details === 'string' ? details : JSON.stringify(details, null, 2)}</pre>
            </div>
          )}

          {associatedDocs && associatedDocs.length > 0 && (
            <div className="mt-2 pt-2 border-t border-gray-200">
              <p className="text-xs text-gray-500 font-semibold mb-1">Associated Document(s):</p>
              <ul className="list-disc list-inside pl-1">
                {associatedDocs.map(doc => (
                  <li key={doc.id} className="text-xs text-gray-600">
                    {doc.name} {doc.url ? <a href={doc.url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">(view)</a> : ''}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CheckItem;
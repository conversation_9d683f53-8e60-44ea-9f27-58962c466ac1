// StreamlitStyleChecklist.tsx - Updated to work with new check definitions

import React from "react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { Separator } from "@/components/ui/separator";

// Import new types
import { CheckResult } from "@/lib/checkDefinitions";
import { AnalysisParameters, DocumentFiles } from "@/lib/mainDocumentProcessor";
import CheckItem from "./CheckItem";

interface StreamlitStyleChecklistProps {
  results: Record<string, CheckResult>;
  parameters: AnalysisParameters;
  documents?: DocumentFiles;
}

// Map check IDs to user-friendly display names
const CHECK_DISPLAY_NAMES: Record<string, string> = {
  // Independent Checks
  audit_title: "Audit Title - Independent Auditor's Report",
  address_to_members: "Address to Members Format",
  audit_report_date: "Audit Report Date Verification",
  standalone_consolidated_wording: "Standalone/Consolidated Wording Consistency",
  brsr_brr_check: "BRSR/BRR Check",
  key_audit_matters: "Key Audit Matters Presence",
  audit_trail_software: "Audit-Trail Accounting Software",
  section_197_16: "Section 197(16) Reference",
  company_name_consistency: "Company Name Consistency",
  pkf_signature_block: "PKF Signature Block Verification",
  clause_20: "Clause 20 - Annexure A(CARO)",
  clause_21: "Clause 21 - Annexure A(CARO)",
  benami_property_clause: "Benami Property Clause",

  
  // Conditional Checks
  // caro_clause_xxi: "CARO Clause (xxi) - Consolidated Reports",
  consolidated_wording_consistency: "Consolidated Wording Consistency",
  nbfc_caro_exemptions: "NBFC CARO Exemptions",
  
  // Interlinked Checks
  ar_caro_reference_matching: "Audit Report + CARO Reference Matching",
  ar_ifc_reference_matching: "Audit Report + IFC Reference Matching",
  bs_caro_ppe_check: "Balance Sheet + CARO PPE Check",
  bs_caro_intangible_check: "Balance Sheet + CARO Intangible Assets Check",
  bs_caro_inventory_check: "Balance Sheet + CARO Inventory Check",
  bs_caro_secured_borrowings: "Balance Sheet + CARO Secured Borrowings Check",
  bs_caro_fixed_deposits: "Balance Sheet + CARO Fixed Deposits Check",
  notes_caro_immovable_property: "Notes + CARO Immovable Property Check",
  notes_caro_new_investments: "Notes + CARO New Investments/Loans Check",
  notes_caro_contingent_liabilities: "Notes + CARO Contingent Liabilities Check",
  notes_caro_goods_in_transit: "Notes + CARO Goods in Transit Check",
  notes_caro_aggregate_threshold: "Notes + CARO Aggregate Threshold Check",
  notes_caro_rpt: "Notes + CARO Related Party Transactions Check",
  notes_caro_statutory_dues: "Notes + CARO Statutory Dues Check",
  notes_caro_revaluation: "Notes + CARO Revaluation Check"
};

// Group checks by category
const getChecksByCategory = (results: Record<string, CheckResult>) => {
  const independent: Array<[string, CheckResult]> = [];
  const conditional: Array<[string, CheckResult]> = [];
  const interlinked: Array<[string, CheckResult]> = [];
  
  Object.entries(results).forEach(([checkId, result]) => {
    // Categorize based on check ID patterns
    if (checkId.includes('_caro_') || checkId.includes('_ifc_') || checkId.includes('ar_') && checkId.includes('reference')) {
      interlinked.push([checkId, result]);
    } else if (checkId.includes('consolidated') || checkId.includes('nbfc') || checkId.includes('clause_xxi')) {
      conditional.push([checkId, result]);
    } else {
      independent.push([checkId, result]);
    }
  });
  
  return { independent, conditional, interlinked };
};

const StreamlitStyleChecklist: React.FC<StreamlitStyleChecklistProps> = ({
  results,
  parameters,
  documents = {}
}) => {
  // Check for missing or invalid data
  if (!results || Object.keys(results).length === 0) {
    return (
      <div className="space-y-6 mt-6 p-6 border rounded-lg bg-white shadow-sm">
        <div className="bg-red-500 text-white p-4 rounded-md">
          <h2 className="text-xl font-bold mb-2">Analysis Failed</h2>
          <p>No analysis results available. Please try again or contact support if the issue persists.</p>
        </div>
      </div>
    );
  }

  // Check for date-related issues
  try {
    if (parameters.audit_date) {
      if (parameters.audit_date instanceof Date) {
        parameters.audit_date.getTime();
      } else if (typeof parameters.audit_date === 'number') {
        new Date(parameters.audit_date).getTime();
      } else if (typeof parameters.audit_date === 'string') {
        new Date(parameters.audit_date).getTime();
      } else {
        throw new Error("Invalid date format");
      }
    }
  } catch (err) {
    console.error("Date validation error in StreamlitStyleChecklist:", err);
    return (
      <div className="space-y-6 mt-6 p-6 border rounded-lg bg-white shadow-sm">
        <div className="bg-red-500 text-white p-4 rounded-md">
          <h2 className="text-xl font-bold mb-2">Analysis Failed</h2>
          <p>An error occurred during analysis: Cannot read properties of undefined (reading 'getTime')</p>
          <p className="mt-2 text-sm">This is likely due to an invalid date format. Please try again with a valid date.</p>
        </div>
      </div>
    );
  }

  // Count the number of compliant checks
  const compliantCount = Object.values(results).filter(result => result.isCompliant).length;
  const totalChecks = Object.keys(results).length;
  const compliancePercentage = totalChecks > 0 ? Math.round((compliantCount / totalChecks) * 100) : 0;

  // Group checks by category
  const { independent, conditional, interlinked } = getChecksByCategory(results);

  // Render check item with proper styling
  const renderCheckItem = (checkId: string, result: CheckResult) => {
    const displayName = CHECK_DISPLAY_NAMES[checkId] || checkId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    
    return (
      <div key={checkId} className="mb-4">
        <div className="flex items-start">
          {result.isCompliant ? (
            <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
          ) : (
            <XCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
          )}
          <div className="flex-1">
            <h4 className="font-medium text-base mb-2">{displayName}</h4>
            
            <div className="mb-2">
              {result.isCompliant ? (
                <div className="text-green-600 font-medium flex items-center">
                  <span className="inline-block w-4 h-4 mr-1 bg-green-100 rounded-sm border border-green-300 flex items-center justify-center">
                    <span className="text-green-600 text-xs">✓</span>
                  </span>
                  <span>Compliant</span>
                </div>
              ) : (
                <div className="text-red-600 font-medium flex items-center">
                  <span className="inline-block w-4 h-4 mr-1 bg-red-100 rounded-sm border border-red-300 flex items-center justify-center">
                    <span className="text-red-600 text-xs">✗</span>
                  </span>
                  <span>Non-Compliant</span>
                </div>
              )}
            </div>

            <div className="text-sm">
              <strong>Explanation:</strong> 
              <span className="text-gray-700 ml-1">{result.explanation}</span>
            </div>

            {/* Show extracted data if available */}
            {result.extractedData && (
              <div className="mt-2 text-sm">
                <strong>Details:</strong>
                <div className="bg-gray-50 p-2 rounded mt-1">
                  <pre className="text-xs whitespace-pre-wrap">
                    {typeof result.extractedData === 'string' 
                      ? result.extractedData 
                      : JSON.stringify(result.extractedData, null, 2)
                    }
                  </pre>
                </div>
              </div>
            )}

            {/* Show additional detail if available */}
            {result.detail && (
              <div className="mt-2 text-sm">
                <strong>Additional Information:</strong>
                <p className="text-gray-600 mt-1">{result.detail}</p>
              </div>
            )}
          </div>
        </div>
        <Separator className="mt-3" />
      </div>
    );
  };

  return (
    <div className="space-y-6 mt-6 p-6 border rounded-lg bg-white shadow-sm">
      {/* Header with compliance summary */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold">Analysis Results</h2>
        <div className="text-sm font-medium">
          {compliantCount}/{totalChecks} Checks Passed ({compliancePercentage}% Compliant)
        </div>
      </div>

      {/* Compliance progress bar */}
      <div className="w-full bg-gray-200 rounded-full h-2.5">
        <div
          className={`h-2.5 rounded-full ${
            compliancePercentage < 50 ? "bg-red-500" :
            compliancePercentage < 80 ? "bg-yellow-500" : "bg-green-500"
          }`}
          style={{ width: `${compliancePercentage}%` }}
        ></div>
      </div>

      {/* Results organized by category */}
      <Accordion type="multiple" defaultValue={["independent", "interlinked", "conditional"]}>
        
        {/* Independent Document Checks */}
        {independent.length > 0 && (
          <AccordionItem value="independent" className="border border-gray-200 rounded-md mb-2 overflow-hidden">
            <AccordionTrigger className="text-xl font-bold py-3 px-4 bg-gray-50 hover:bg-gray-100">
              1) Independent Document Checks ({independent.length})
            </AccordionTrigger>
            <AccordionContent>
              <div className="pl-6 pr-4 pt-4 pb-2">
                <h3 className="text-lg font-semibold mb-4 text-blue-800">Single Document Analysis</h3>
                {independent.map(([checkId, result]) => renderCheckItem(checkId, result))}
              </div>
            </AccordionContent>
          </AccordionItem>
        )}

        {/* Interlinked Document Checks */}
        {interlinked.length > 0 && (
          <AccordionItem value="interlinked" className="border border-gray-200 rounded-md mb-2 overflow-hidden">
            <AccordionTrigger className="text-xl font-bold py-3 px-4 bg-gray-50 hover:bg-gray-100">
              2) Interlinked Document Checks ({interlinked.length})
            </AccordionTrigger>
            <AccordionContent>
              <div className="pl-6 pr-4 pt-4 pb-2">
                <h3 className="text-lg font-semibold mb-4 text-blue-800">Multi-Document Comparisons</h3>
                {interlinked.map(([checkId, result]) => renderCheckItem(checkId, result))}
              </div>
            </AccordionContent>
          </AccordionItem>
        )}

        {/* Conditional Checks */}
        {conditional.length > 0 && (
          <AccordionItem value="conditional" className="border border-gray-200 rounded-md mb-2 overflow-hidden">
            <AccordionTrigger className="text-xl font-bold py-3 px-4 bg-gray-50 hover:bg-gray-100">
              3) Conditional Checks ({conditional.length})
            </AccordionTrigger>
            <AccordionContent>
              <div className="pl-6 pr-4 pt-4 pb-2">
                <h3 className="text-lg font-semibold mb-4 text-blue-800">Based on User Selections</h3>
                <div className="mb-4 text-sm bg-blue-50 p-3 rounded-md">
                  <strong>Applied Conditions:</strong>
                  <ul className="mt-2 list-disc list-inside">
                    <li>Report Type: {parameters.audit_report_type}</li>
                    <li>Company Status: {parameters.company_listing_status}</li>
                    {parameters.company_listing_status === 'Listed' && (
                      <li>Top 1000/500: {parameters.top_1000_or_500}</li>
                    )}
                    <li>NBFC Status: {parameters.is_nbfc}</li>
                  </ul>
                </div>
                {conditional.map(([checkId, result]) => renderCheckItem(checkId, result))}
              </div>
            </AccordionContent>
          </AccordionItem>
        )}
      </Accordion>

      {/* Summary information */}
      <div className="mt-6 flex items-center text-sm bg-green-50 p-3 rounded-md">
        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
        <p>
          Analysis completed successfully. {compliantCount} out of {totalChecks} checks passed, 
          resulting in a {compliancePercentage}% compliance rate.
        </p>
      </div>
    </div>
  );
};

export default StreamlitStyleChecklist;